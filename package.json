{"name": "tiptap-project", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@tiptap/core": "^3.3.0", "@tiptap/extension-color": "^3.3.0", "@tiptap/extension-document": "^3.3.0", "@tiptap/extension-paragraph": "^3.3.0", "@tiptap/extension-text": "^3.3.0", "@tiptap/extension-text-style": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "@tiptap/vue-3": "^3.3.0", "core-js": "^3.8.3", "vue": "^3.2.13"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "css-loader": "^7.1.2", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.91.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "webpack": "^5.101.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}